.notificationsContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.notificationsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.titleContainer {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notificationsTitle {
  font-size: 1.75rem;
  font-weight: 600;
  color: #10b981;
  margin: 0;
}

.refreshIndicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #65676b;
  opacity: 0.8;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e4e6ea;
  border-top: 2px solid #10b981;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.notificationsActions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.headerActions {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.deleteAllButton {
  color: #e41e3f !important;
  border-color: #e41e3f !important;
}

.deleteAllButton:hover {
  background-color: #e41e3f !important;
  color: white !important;
}

.loading, .emptyNotifications {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  text-align: center;
  color: #65676b;
}

.notificationsList {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.notificationItem {
  display: flex;
  padding: 1rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s;
}

.notificationItem:hover {
  background-color: #f0f2f5;
}

.unread {
  background-color: #e6f2ff;
}

.unread:hover {
  background-color: #d9ecff;
}

.notificationSender {
  margin-right: 1rem;
  text-decoration: none;
  color: inherit;
}

.senderAvatar {
  border-radius: 50%;
  object-fit: cover;
}

.senderAvatarPlaceholder {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.25rem;
}

.notificationContent {
  flex: 1;
}

.notificationHeader {
  margin-bottom: 0.5rem;
}

.senderName {
  font-weight: 600;
  text-decoration: none;
  color: inherit;
  margin-right: 0.25rem;
}

.notificationText {
  color: #050505;
}

.notificationTime {
  font-size: 0.8rem;
  color: #65676b;
}

.notificationActions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
  position: relative;
}

.deleteButton {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #65676b;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  opacity: 0;
}

.notificationItem:hover .deleteButton {
  opacity: 1;
}

.deleteButton:hover {
  background-color: #f0f2f5;
  color: #e41e3f;
  transform: scale(1.1);
}

.unreadIndicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #10b981;
}

@media (max-width: 576px) {
  .notificationsHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .titleContainer {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .refreshIndicator {
    font-size: 0.8rem;
  }

  .notificationsActions {
    flex-wrap: wrap;
  }

  .notificationItem {
    flex-direction: column;
  }

  .notificationSender {
    margin-right: 0;
    margin-bottom: 0.75rem;
  }

  .notificationActions {
    flex-direction: row;
    justify-content: flex-end;
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .deleteButton {
    opacity: 1;
  }

  .headerActions {
    flex-wrap: wrap;
  }
}
