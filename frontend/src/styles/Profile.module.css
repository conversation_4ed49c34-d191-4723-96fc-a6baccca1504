.profileContainer {
  max-width: 1000px;
  margin: 0 auto;
  padding: 1rem;
}

/* Update message styles */
.updateMessage {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-weight: 500;
  text-align: center;
  animation: slideIn 0.3s ease-out;
}

.updateMessage.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.updateMessage.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.loading, .notFound {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  text-align: center;
}

.notFound h1 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #10b981;
}

.notFound p {
  color: #65676b;
}

/* Profile Header */
.profileHeader {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.profileCover {
  height: 200px;
  position: relative;
  background-color: #f0f2f5;
}

.defaultCover {
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #10b981, #0f9467);
}

.profileInfo {
  padding: 0 1.5rem 1.5rem;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start; /* Align items to top */
}

.profilePicture {
  margin-top: -60px;
  margin-right: 1.5rem;
  position: relative;
  z-index: 1;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  border: 4px solid white;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  font-weight: bold;
  border: 4px solid white;
}

.profileDetails {
  flex: 1;
  padding-top: 1rem;
  min-width: 0; /* Allow flex item to shrink below content size */
  display: flex;
  flex-direction: column;
}

.profileName {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 0.25rem;
}

.profileUsernameRow {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.profileUsername {
  color: #65676b;
  margin: 0;
}

.privacyTag {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.privateTag {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fbbf24;
}

.publicTag {
  background-color: #d1fae5;
  color: #065f46;
  border: 1px solid #10b981;
}

.profileBio {
  margin-bottom: 1rem;
  line-height: 1.5;
}

.profileBirthdate {
  margin-bottom: 0.5rem;
  color: #65676b;
  font-size: 0.875rem;
}

.profileStats {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
}

.stat {
  display: flex;
  flex-direction: column;
}

.statCount {
  font-weight: 600;
  font-size: 1.25rem;
}

.statLabel {
  color: #65676b;
  font-size: 0.875rem;
}

.profileActions {
  margin-top: 1.5rem;
  display: flex;
  gap: 1rem;
  align-self: flex-start; /* Prevent stretching with parent */
  flex-shrink: 0; /* Prevent shrinking */
  width: auto; /* Don't inherit parent width */
}

/* Profile Content */
.profileContent {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profileTabs {
  display: flex;
  border-bottom: 1px solid #dddfe2;
}

.tabButton {
  flex: 1;
  padding: 1rem;
  background: none;
  border: none;
  font-weight: 500;
  color: #65676b;
  cursor: pointer;
  transition: all 0.2s;
}

.tabButton:hover {
  background-color: #f0f2f5;
}

.activeTab {
  color: #10b981;
  border-bottom: 2px solid #10b981;
}

.tabContent {
  padding: 1.5rem;
  height: 600px; /* Fixed height */
  overflow-y: auto; /* Enable vertical scrolling */
  overflow-x: hidden; /* Hide horizontal scrolling */
  scroll-behavior: smooth; /* Smooth scrolling */
}

/* Custom scrollbar styling */
.tabContent::-webkit-scrollbar {
  width: 8px;
}

.tabContent::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.tabContent::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.tabContent::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  color: #65676b;
  text-align: center;
}

.postsGrid, .followersGrid, .followingGrid {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.postsPlaceholder, .followersPlaceholder, .followingPlaceholder {
  grid-column: 1 / -1;
  min-height: 200px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px dashed #dddfe2;
  border-radius: 8px;
  color: #65676b;
}

.postsList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.usersList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.followersGrid,
.followingGrid {
  width: 100%;
}

.emptyState p {
  margin: 0.5rem 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .profileInfo {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profilePicture {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .profileStats {
    justify-content: center;
  }

  .profileActions {
    justify-content: center;
  }

  .tabContent {
    height: 400px; /* Smaller height on mobile */
  }
}

/* Modal styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modalContent {
  background-color: white;
  border-radius: 8px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.input, .textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #dddfe2;
  border-radius: 4px;
  font-size: 1rem;
}

.textarea {
  resize: vertical;
}

.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.modalActions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
}

.fileInput {
  display: block;
  width: 100%;
  padding: 0.5rem 0;
  margin-bottom: 0.5rem;
}

.imagePreview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin-top: 0.5rem;
}

.imagePreview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.coverImagePreview {
  width: 200px;
  height: 100px;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 0.5rem;
}

.coverImagePreview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.coverPhotoUpload {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
}

.profilePicUpload {
  position: absolute;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
}
