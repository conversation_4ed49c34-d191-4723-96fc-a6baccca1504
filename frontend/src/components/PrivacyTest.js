'use client';

import React from 'react';
import { canViewFullProfile, canViewPosts, canViewFollowers, canViewFollowing, getPrivacyStatus } from '@/utils/privacy';

/**
 * Test component to demonstrate privacy functionality
 * This is for testing purposes only
 */
const PrivacyTest = () => {
  // Mock data for testing
  const currentUser = { id: 'user1', username: 'currentuser' };
  
  const publicProfile = {
    user: { id: 'user2', username: 'publicuser', isPrivate: false },
    isFollowedByCurrentUser: false
  };
  
  const privateProfileFollowed = {
    user: { id: 'user3', username: 'privateuser', isPrivate: true },
    isFollowedByCurrentUser: true
  };
  
  const privateProfileNotFollowed = {
    user: { id: 'user4', username: 'privateusernotfollowed', isPrivate: true },
    isFollowedByCurrentUser: false
  };

  const ownProfile = {
    user: { id: 'user1', username: 'currentuser', isPrivate: true },
    isFollowedByCurrentUser: false
  };

  const testCases = [
    { name: 'Own Profile', profile: ownProfile },
    { name: 'Public Profile', profile: publicProfile },
    { name: 'Private Profile (Followed)', profile: privateProfileFollowed },
    { name: 'Private Profile (Not Followed)', profile: privateProfileNotFollowed }
  ];

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h2>Privacy Functionality Test</h2>
      <table style={{ borderCollapse: 'collapse', width: '100%' }}>
        <thead>
          <tr style={{ backgroundColor: '#f5f5f5' }}>
            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Profile Type</th>
            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Can View Profile</th>
            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Can View Posts</th>
            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Can View Followers</th>
            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Can View Following</th>
            <th style={{ border: '1px solid #ddd', padding: '8px' }}>Privacy Status</th>
          </tr>
        </thead>
        <tbody>
          {testCases.map((testCase, index) => {
            const canViewProfile = canViewFullProfile(testCase.profile, currentUser);
            const canViewProfilePosts = canViewPosts(testCase.profile, currentUser);
            const canViewProfileFollowers = canViewFollowers(testCase.profile, currentUser);
            const canViewProfileFollowing = canViewFollowing(testCase.profile, currentUser);
            const privacyStatus = getPrivacyStatus(testCase.profile.user);

            return (
              <tr key={index}>
                <td style={{ border: '1px solid #ddd', padding: '8px' }}>{testCase.name}</td>
                <td style={{ border: '1px solid #ddd', padding: '8px', color: canViewProfile ? 'green' : 'red' }}>
                  {canViewProfile ? '✅ Yes' : '❌ No'}
                </td>
                <td style={{ border: '1px solid #ddd', padding: '8px', color: canViewProfilePosts ? 'green' : 'red' }}>
                  {canViewProfilePosts ? '✅ Yes' : '❌ No'}
                </td>
                <td style={{ border: '1px solid #ddd', padding: '8px', color: canViewProfileFollowers ? 'green' : 'red' }}>
                  {canViewProfileFollowers ? '✅ Yes' : '❌ No'}
                </td>
                <td style={{ border: '1px solid #ddd', padding: '8px', color: canViewProfileFollowing ? 'green' : 'red' }}>
                  {canViewProfileFollowing ? '✅ Yes' : '❌ No'}
                </td>
                <td style={{ border: '1px solid #ddd', padding: '8px' }}>
                  {privacyStatus.icon} {privacyStatus.label}
                </td>
              </tr>
            );
          })}
        </tbody>
      </table>
      
      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#e8f5e8', borderRadius: '5px' }}>
        <h3>✅ Privacy Implementation Complete!</h3>
        <p>The privacy functionality has been successfully implemented with:</p>
        <ul>
          <li>✅ Privacy utility functions for permission checking</li>
          <li>✅ Reusable privacy-restricted components</li>
          <li>✅ Profile page with conditional rendering</li>
          <li>✅ Visual indicators for private accounts</li>
          <li>✅ Appropriate privacy messages</li>
          <li>✅ Consistent UX across all views</li>
        </ul>
      </div>
    </div>
  );
};

export default PrivacyTest;
